'use strict'

/**
 * 定时任务管理器
 * 
 * 负责定时任务的创建、执行、状态管理等核心功能
 * 主要功能：
 * 1. 创建和管理定时上下架任务
 * 2. 检查和执行到期任务
 * 3. 任务状态跟踪和重试机制
 * 4. 监控状态联动管理
 */
class ScheduledTaskManager {
  constructor() {
    this.db = uniCloud.database()
    this.dbCmd = this.db.command
    this.tasksCollection = this.db.collection('scheduled-tasks')
    this.shelvesCollection = this.db.collection('account-shelves')
    
    // 任务状态常量
    this.TASK_STATUS = {
      PENDING: 'pending',
      COMPLETED: 'completed',
      FAILED: 'failed',
      CANCELLED: 'cancelled'
    }
    
    // 任务类型常量
    this.TASK_TYPES = {
      ON_SHELF: 'on_shelf',
      OFF_SHELF: 'off_shelf'
    }
    
    // 目标类型常量
    this.TARGET_TYPES = {
      ACCOUNT: 'account',
      SHELF: 'shelf'
    }
    
    // 最大重试次数
    this.MAX_RETRY_COUNT = 3
    
    // 重试间隔（分钟）
    this.RETRY_INTERVAL_MINUTES = 5
  }

  /**
   * 创建定时任务
   * @param {Object} taskData 任务数据
   * @param {string} taskData.userId 用户ID
   * @param {string} taskData.taskType 任务类型(on_shelf/off_shelf)
   * @param {string} taskData.targetType 目标类型(account/shelf)
   * @param {string} taskData.targetId 目标ID
   * @param {string} taskData.platformType 平台类型（shelf类型时必填）
   * @param {Object} taskData.relativeTime 相对时间配置 {days, hours, minutes}
   * @param {boolean} taskData.autoDisableMonitor 是否自动禁用监控
   * @param {string} taskData.description 任务描述
   * @returns {Object} 创建结果
   */
  async createTask(taskData) {
    try {
      // 参数验证
      this._validateTaskData(taskData)
      
      // 计算执行时间
      const executeTime = this._calculateExecuteTime(taskData.relativeTime)
      
      // 构建任务记录
      const now = Date.now()
      const taskRecord = {
        user_id: taskData.userId,
        task_type: taskData.taskType,
        target_type: taskData.targetType,
        target_id: taskData.targetId,
        platform_type: taskData.platformType || null,
        execute_time: executeTime,
        relative_time_config: taskData.relativeTime,
        status: this.TASK_STATUS.PENDING,
        auto_disable_monitor: taskData.autoDisableMonitor !== false,
        description: taskData.description || this._generateTaskDescription(taskData),
        created_by: 'manual',
        retry_count: 0,
        create_time: now,
        update_time: now
      }
      
      // 插入数据库
      const result = await this.tasksCollection.add(taskRecord)
      
      return {
        success: true,
        taskId: result.id,
        executeTime: executeTime,
        message: '定时任务创建成功'
      }
      
    } catch (error) {
      console.error('创建定时任务失败:', error)
      return {
        success: false,
        message: error.message || '创建定时任务失败'
      }
    }
  }

  /**
   * 获取待执行的任务列表
   * @returns {Array} 待执行任务列表
   */
  async getPendingTasks() {
    try {
      const now = Date.now()
      
      const { data } = await this.tasksCollection
        .where({
          status: this.TASK_STATUS.PENDING,
          execute_time: this.dbCmd.lte(now)
        })
        .orderBy('execute_time', 'asc')
        .get()
      
      return data
      
    } catch (error) {
      console.error('获取待执行任务失败:', error)
      return []
    }
  }

  /**
   * 获取需要重试的任务列表
   * @returns {Array} 需要重试的任务列表
   */
  async getRetryTasks() {
    try {
      const now = Date.now()
      
      const { data } = await this.tasksCollection
        .where({
          status: this.TASK_STATUS.FAILED,
          retry_count: this.dbCmd.lt(this.MAX_RETRY_COUNT),
          next_retry_time: this.dbCmd.lte(now)
        })
        .orderBy('next_retry_time', 'asc')
        .get()
      
      return data
      
    } catch (error) {
      console.error('获取重试任务失败:', error)
      return []
    }
  }

  /**
   * 更新任务状态
   * @param {string} taskId 任务ID
   * @param {string} status 新状态
   * @param {Object} additionalData 附加数据
   */
  async updateTaskStatus(taskId, status, additionalData = {}) {
    try {
      const updateData = {
        status: status,
        update_time: Date.now(),
        ...additionalData
      }

      // 根据状态设置特定字段
      if (status === this.TASK_STATUS.COMPLETED || status === this.TASK_STATUS.FAILED) {
        updateData.execute_end_time = Date.now()
      }

      await this.tasksCollection.doc(taskId).update(updateData)

    } catch (error) {
      console.error('更新任务状态失败:', error)
    }
  }

  /**
   * 取消任务
   * @param {string} taskId 任务ID
   * @param {string} userId 用户ID（权限验证）
   */
  async cancelTask(taskId, userId) {
    try {
      const result = await this.tasksCollection
        .where({
          _id: taskId,
          user_id: userId,
          status: this.dbCmd.in([this.TASK_STATUS.PENDING, this.TASK_STATUS.FAILED])
        })
        .update({
          status: this.TASK_STATUS.CANCELLED,
          update_time: Date.now()
        })

      return {
        success: result.updated > 0,
        message: result.updated > 0 ? '任务取消成功' : '任务不存在或无法取消'
      }

    } catch (error) {
      console.error('取消任务失败:', error)
      return {
        success: false,
        message: '取消任务失败'
      }
    }
  }

  /**
   * 删除任务
   * @param {string} taskId 任务ID
   * @param {string} userId 用户ID（权限验证）
   */
  async deleteTask(taskId, userId) {
    try {
      // 先查询任务是否存在且属于该用户
      const { data } = await this.tasksCollection
        .where({
          _id: taskId,
          user_id: userId
        })
        .get()

      if (data.length === 0) {
        return {
          success: false,
          message: '任务不存在或无权限删除'
        }
      }

      // 物理删除任务记录
      const result = await this.tasksCollection
        .doc(taskId)
        .remove()

      console.log(`定时任务删除成功: ${taskId}, 删除记录数: ${result.deleted}`)

      return {
        success: result.deleted > 0,
        message: result.deleted > 0 ? '任务删除成功' : '删除失败',
        deletedCount: result.deleted
      }

    } catch (error) {
      console.error('删除任务失败:', error)
      return {
        success: false,
        message: error.message || '删除任务失败'
      }
    }
  }

  /**
   * 获取用户的任务列表
   * @param {string} userId 用户ID
   * @param {Object} options 查询选项
   */
  async getUserTasks(userId, options = {}) {
    try {
      const {
        status = null,
        taskType = null,
        pageIndex = 1,
        pageSize = 20
      } = options

      let query = this.tasksCollection.where({ user_id: userId })

      // 状态筛选
      if (status) {
        query = query.where({ status: status })
      }

      // 任务类型筛选
      if (taskType) {
        query = query.where({ task_type: taskType })
      }

      // 分页查询
      const { data } = await query
        .orderBy('create_time', 'desc')
        .skip((pageIndex - 1) * pageSize)
        .limit(pageSize)
        .get()

      // 增强任务信息，添加关联的账号、平台、货架详细信息
      const enhancedTasks = await this._enhanceTasksWithDetails(data, userId)

      return {
        list: enhancedTasks,
        hasMore: data.length === pageSize
      }

    } catch (error) {
      console.error('获取用户任务列表失败:', error)
      return {
        list: [],
        hasMore: false
      }
    }
  }

  /**
   * 增强任务信息，添加关联的详细信息
   * @private
   * @param {Array} tasks 任务列表
   * @param {string} userId 用户ID
   * @returns {Array} 增强后的任务列表
   */
  async _enhanceTasksWithDetails(tasks, userId) {
    if (tasks.length === 0) return tasks

    try {
      // 获取所有相关的货架数据
      const { data: shelves } = await this.shelvesCollection
        .where({ user_id: userId })
        .get()

      // 获取平台配置信息
      const { data: platformConfigs } = await this.db.collection('platform-configs')
        .where({ user_id: userId })
        .get()

      // 创建平台名称映射
      const platformNameMap = {}
      platformConfigs.forEach(config => {
        platformNameMap[config.platform_type] = config.platform_name
      })

      // 为每个任务添加详细信息
      return tasks.map(task => {
        const enhancedTask = { ...task }

        if (task.target_type === 'account') {
          // 账号级别任务：获取该账号下所有启用监控的平台和货架信息
          enhancedTask.accountDetails = this._getAccountDetails(task.target_id, shelves, platformNameMap)
        } else if (task.target_type === 'shelf') {
          // 货架级别任务：获取具体货架的详细信息
          enhancedTask.shelfDetails = this._getShelfDetails(task.target_id, task.platform_type, shelves, platformNameMap)
        }

        return enhancedTask
      })

    } catch (error) {
      console.error('增强任务信息失败:', error)
      // 如果增强失败，返回原始任务列表
      return tasks
    }
  }

  /**
   * 获取账号详细信息
   * @private
   * @param {string} gameAccount 游戏账号名
   * @param {Array} shelves 所有货架数据
   * @param {Object} platformNameMap 平台名称映射
   * @returns {Object} 账号详细信息
   */
  _getAccountDetails(gameAccount, shelves, platformNameMap) {
    // 筛选该账号下的所有货架（不限制是否启用监控）
    const accountShelves = shelves.filter(shelf =>
      shelf.game_account === gameAccount
    )

    // 按平台分组统计
    const platformStats = {}
    accountShelves.forEach(shelf => {
      if (!platformStats[shelf.platform_type]) {
        platformStats[shelf.platform_type] = {
          platform_name: platformNameMap[shelf.platform_type] || shelf.platform_type,
          shelf_count: 0,
          active_count: 0,
          shelves: []
        }
      }
      platformStats[shelf.platform_type].shelf_count++
      if (shelf.is_active) {
        platformStats[shelf.platform_type].active_count++
      }
      platformStats[shelf.platform_type].shelves.push({
        _id: shelf._id,
        shelf_title: shelf.shelf_title,
        game_name: shelf.game_name,
        is_active: shelf.is_active
      })
    })

    return {
      game_account: gameAccount,
      total_shelves: accountShelves.length,
      platforms: Object.values(platformStats)
    }
  }

  /**
   * 获取货架详细信息
   * @private
   * @param {string} shelfId 货架ID
   * @param {string} platformType 平台类型
   * @param {Array} shelves 所有货架数据
   * @param {Object} platformNameMap 平台名称映射
   * @returns {Object} 货架详细信息
   */
  _getShelfDetails(shelfId, platformType, shelves, platformNameMap) {
    const shelf = shelves.find(s => s._id === shelfId)

    if (!shelf) {
      return {
        shelf_id: shelfId,
        platform_type: platformType,
        platform_name: platformNameMap[platformType] || platformType,
        game_account: '未知',
        shelf_title: '货架不存在或已删除',
        game_name: ''
      }
    }

    return {
      shelf_id: shelf._id,
      platform_type: shelf.platform_type,
      platform_name: platformNameMap[shelf.platform_type] || shelf.platform_type,
      game_account: shelf.game_account,
      shelf_title: shelf.shelf_title,
      game_name: shelf.game_name,
      rent_price: shelf.rent_price
    }
  }

  /**
   * 验证任务数据
   * @private
   */
  _validateTaskData(taskData) {
    const { userId, taskType, targetType, targetId, relativeTime } = taskData
    
    if (!userId) throw new Error('用户ID不能为空')
    if (!Object.values(this.TASK_TYPES).includes(taskType)) {
      throw new Error('无效的任务类型')
    }
    if (!Object.values(this.TARGET_TYPES).includes(targetType)) {
      throw new Error('无效的目标类型')
    }
    if (!targetId) throw new Error('目标ID不能为空')
    if (!relativeTime || typeof relativeTime !== 'object') {
      throw new Error('相对时间配置不能为空')
    }
    
    // 验证相对时间
    const { days = 0, hours = 0, minutes = 0 } = relativeTime
    if (days < 0 || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
      throw new Error('相对时间配置无效')
    }
    if (days === 0 && hours === 0 && minutes === 0) {
      throw new Error('相对时间不能为0')
    }
    
    // shelf类型需要平台类型
    if (targetType === this.TARGET_TYPES.SHELF && !taskData.platformType) {
      throw new Error('货架类型任务必须指定平台类型')
    }
  }

  /**
   * 计算执行时间
   * @private
   */
  _calculateExecuteTime(relativeTime) {
    const { days = 0, hours = 0, minutes = 0 } = relativeTime
    const now = Date.now()
    const totalMinutes = days * 24 * 60 + hours * 60 + minutes
    return now + totalMinutes * 60 * 1000
  }

  /**
   * 生成任务描述
   * @private
   */
  _generateTaskDescription(taskData) {
    const { taskType, targetType, targetId, relativeTime } = taskData
    const { days = 0, hours = 0, minutes = 0 } = relativeTime
    
    const action = taskType === this.TASK_TYPES.ON_SHELF ? '上架' : '下架'
    const target = targetType === this.TARGET_TYPES.ACCOUNT ? `账号 ${targetId}` : `货架 ${targetId}`
    
    const timeStr = []
    if (days > 0) timeStr.push(`${days}天`)
    if (hours > 0) timeStr.push(`${hours}小时`)
    if (minutes > 0) timeStr.push(`${minutes}分钟`)
    
    return `${timeStr.join('')}后自动${action} ${target}`
  }
}

module.exports = ScheduledTaskManager
